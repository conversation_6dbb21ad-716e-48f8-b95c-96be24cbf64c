{"logs": [{"outputFile": "rw.health.ubuzima.ubuzima_app-mergeDebugResources-72:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "56,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6136,7108,7220,7334", "endColumns": "100,111,113,107", "endOffsets": "6232,7215,7329,7437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21f508358b7ef6793f647ddb068091fd\\transformed\\appcompat-1.2.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1946,2059,2160,2256,2369,2479,2603,2777,2888", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "211,322,430,521,628,755,839,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1941,2054,2155,2251,2364,2474,2598,2772,2883,2963"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,327,435,526,633,760,844,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1946,2059,2160,2256,2369,2479,2603,2777,8983", "endColumns": "110,110,107,90,106,126,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,123,173,110,79", "endOffsets": "211,322,430,521,628,755,839,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1941,2054,2155,2251,2364,2474,2598,2772,2883,9058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4650", "endColumns": "163", "endOffsets": "4809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3608,3719,3877,4011,4128,4299,4435,4545,4814,4994,5108,5272,5405,5553,5705,5771,5843", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "3714,3872,4006,4123,4294,4430,4540,4645,4989,5103,5267,5400,5548,5700,5766,5838,5930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bd1be39a4121b41df3cecdb1f1acb24d\\transformed\\biometric-1.1.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,263,385,513,645,790,922,1070,1166,1306,1445", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "168,258,380,508,640,785,917,1065,1161,1301,1440,1571"}, "to": {"startLines": "54,57,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5935,6237,7442,7564,7692,7824,7969,8101,8249,8345,8485,8624", "endColumns": "117,89,121,127,131,144,131,147,95,139,138,130", "endOffsets": "6048,6322,7559,7687,7819,7964,8096,8244,8340,8480,8619,8750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,188,272,353,500,669,767", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "183,267,348,495,664,762,842"}, "to": {"startLines": "55,67,81,82,85,86,87", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6053,7024,8755,8836,9164,9333,9431", "endColumns": "82,83,80,146,168,97,79", "endOffsets": "6131,7103,8831,8978,9328,9426,9506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6327,6398,6469,6539,6606,6684,6761,6861,6955", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "6393,6464,6534,6601,6679,6756,6856,6950,7019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "29,30,31,32,33,34,35,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2888,2991,3094,3196,3302,3400,3500,9063", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "2986,3089,3191,3297,3395,3495,3603,9159"}}]}]}