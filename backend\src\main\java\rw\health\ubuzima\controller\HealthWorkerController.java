package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.entity.HealthRecord;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.repository.AppointmentRepository;
import rw.health.ubuzima.repository.HealthRecordRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/health-worker")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class HealthWorkerController {

    private final UserRepository userRepository;
    private final AppointmentRepository appointmentRepository;
    private final HealthRecordRepository healthRecordRepository;

    // Get assigned clients
    @GetMapping("/{healthWorkerId}/clients")
    public ResponseEntity<Map<String, Object>> getAssignedClients(@PathVariable Long healthWorkerId) {
        try {
            User healthWorker = userRepository.findById(healthWorkerId).orElse(null);
            
            if (healthWorker == null || !healthWorker.isHealthWorker()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Health worker not found"
                ));
            }

            // Get clients who have appointments with this health worker
            List<Appointment> appointments = appointmentRepository.findByHealthWorker(healthWorker);
            List<User> clients = appointments.stream()
                .map(Appointment::getUser)
                .distinct()
                .collect(Collectors.toList());

            List<UserResponse> clientResponses = clients.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "clients", clientResponses
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch clients: " + e.getMessage()
            ));
        }
    }

    // Get appointments for health worker
    @GetMapping("/{healthWorkerId}/appointments")
    public ResponseEntity<Map<String, Object>> getAppointments(
            @PathVariable Long healthWorkerId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String date) {
        
        try {
            User healthWorker = userRepository.findById(healthWorkerId).orElse(null);
            
            if (healthWorker == null || !healthWorker.isHealthWorker()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Health worker not found"
                ));
            }

            List<Appointment> appointments;
            
            if (status != null) {
                AppointmentStatus appointmentStatus = AppointmentStatus.valueOf(status.toUpperCase());
                appointments = appointmentRepository.findByHealthWorker(healthWorker)
                    .stream()
                    .filter(apt -> apt.getStatus() == appointmentStatus)
                    .collect(Collectors.toList());
            } else {
                appointments = appointmentRepository.findByHealthWorker(healthWorker);
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "appointments", appointments
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch appointments: " + e.getMessage()
            ));
        }
    }

    // Update appointment status
    @PutMapping("/appointments/{appointmentId}/status")
    public ResponseEntity<Map<String, Object>> updateAppointmentStatus(
            @PathVariable Long appointmentId,
            @RequestBody Map<String, String> request) {
        
        try {
            Appointment appointment = appointmentRepository.findById(appointmentId).orElse(null);
            
            if (appointment == null) {
                return ResponseEntity.notFound().build();
            }

            String statusStr = request.get("status");
            AppointmentStatus status = AppointmentStatus.valueOf(statusStr.toUpperCase());
            
            appointment.setStatus(status);
            
            if (status == AppointmentStatus.COMPLETED) {
                appointment.setCompletedAt(LocalDateTime.now());
            } else if (status == AppointmentStatus.CANCELLED) {
                appointment.setCancelledAt(LocalDateTime.now());
                appointment.setCancellationReason(request.get("reason"));
            }

            appointmentRepository.save(appointment);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment status updated successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update appointment: " + e.getMessage()
            ));
        }
    }

    // Get client health records
    @GetMapping("/clients/{clientId}/health-records")
    public ResponseEntity<Map<String, Object>> getClientHealthRecords(@PathVariable Long clientId) {
        try {
            User client = userRepository.findById(clientId).orElse(null);
            
            if (client == null || !client.isClient()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Client not found"
                ));
            }

            List<HealthRecord> healthRecords = healthRecordRepository.findByUserOrderByRecordedAtDesc(client);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "healthRecords", healthRecords
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch health records: " + e.getMessage()
            ));
        }
    }

    // Add health record for client
    @PostMapping("/clients/{clientId}/health-records")
    public ResponseEntity<Map<String, Object>> addHealthRecord(
            @PathVariable Long clientId,
            @RequestBody Map<String, Object> request) {
        
        try {
            User client = userRepository.findById(clientId).orElse(null);
            
            if (client == null || !client.isClient()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Client not found"
                ));
            }

            HealthRecord healthRecord = new HealthRecord();
            healthRecord.setUser(client);
            // Set other fields from request
            // healthRecord.setRecordType(...);
            // healthRecord.setValue(...);
            // etc.

            HealthRecord savedRecord = healthRecordRepository.save(healthRecord);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Health record added successfully",
                "healthRecord", savedRecord
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to add health record: " + e.getMessage()
            ));
        }
    }

    // Get health worker dashboard stats
    @GetMapping("/{healthWorkerId}/dashboard/stats")
    public ResponseEntity<Map<String, Object>> getDashboardStats(@PathVariable Long healthWorkerId) {
        try {
            User healthWorker = userRepository.findById(healthWorkerId).orElse(null);
            
            if (healthWorker == null || !healthWorker.isHealthWorker()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Health worker not found"
                ));
            }

            List<Appointment> allAppointments = appointmentRepository.findByHealthWorker(healthWorker);
            long totalAppointments = allAppointments.size();
            long todayAppointments = allAppointments.stream()
                .filter(apt -> apt.getScheduledDate().toLocalDate().equals(LocalDateTime.now().toLocalDate()))
                .count();
            long completedAppointments = allAppointments.stream()
                .filter(apt -> apt.getStatus() == AppointmentStatus.COMPLETED)
                .count();
            long totalClients = allAppointments.stream()
                .map(Appointment::getUser)
                .distinct()
                .count();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalAppointments", totalAppointments);
            stats.put("todayAppointments", todayAppointments);
            stats.put("completedAppointments", completedAppointments);
            stats.put("totalClients", totalClients);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "stats", stats
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch dashboard stats: " + e.getMessage()
            ));
        }
    }

    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(Long.valueOf(user.getId().toString()));
        response.setName(user.getName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setRole(user.getRole());
        response.setFacilityId(user.getFacilityId());
        response.setDistrict(user.getDistrict());
        response.setSector(user.getSector());
        response.setCell(user.getCell());
        response.setVillage(user.getVillage());
        response.setCreatedAt(user.getCreatedAt());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setActive(user.isActive());
        response.setProfileImageUrl(user.getProfilePictureUrl());
        return response;
    }

    @GetMapping("/clients/{clientId}")
    public ResponseEntity<Map<String, Object>> getClientDetails(@PathVariable Long clientId) {
        try {
            User client = userRepository.findById(clientId).orElse(null);

            if (client == null || !client.isClient()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Client not found"
                ));
            }

            // Get client's health records
            List<HealthRecord> healthRecords = healthRecordRepository.findByUser(client);

            // Get client's appointments
            List<Appointment> appointments = appointmentRepository.findByUser(client);

            Map<String, Object> clientDetails = new HashMap<>();
            clientDetails.put("id", client.getId());
            clientDetails.put("name", client.getName());
            clientDetails.put("email", client.getEmail());
            clientDetails.put("phone", client.getPhone());
            clientDetails.put("district", client.getDistrict());
            clientDetails.put("sector", client.getSector());
            clientDetails.put("cell", client.getCell());
            clientDetails.put("village", client.getVillage());
            clientDetails.put("healthRecords", healthRecords);
            clientDetails.put("appointments", appointments);
            clientDetails.put("totalHealthRecords", healthRecords.size());
            clientDetails.put("totalAppointments", appointments.size());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "clientDetails", clientDetails
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch client details: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/consultations")
    public ResponseEntity<Map<String, Object>> createConsultation(@RequestBody Map<String, Object> request) {
        try {
            Long clientId = Long.valueOf(request.get("clientId").toString());
            Long healthWorkerId = Long.valueOf(request.get("healthWorkerId").toString());

            User client = userRepository.findById(clientId).orElse(null);
            User healthWorker = userRepository.findById(healthWorkerId).orElse(null);

            if (client == null || !client.isClient()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Client not found"
                ));
            }

            if (healthWorker == null || !healthWorker.isHealthWorker()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Health worker not found"
                ));
            }

            // Create consultation record (this could be a separate entity)
            HealthRecord consultation = new HealthRecord();
            consultation.setUser(client);
            consultation.setRecordType(RecordType.CONSULTATION);
            consultation.setValue(request.get("notes").toString());
            consultation.setRecordedBy(healthWorker.getName());
            consultation.setRecordedAt(LocalDateTime.now());
            consultation.setIsVerified(true);

            HealthRecord savedConsultation = healthRecordRepository.save(consultation);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Consultation recorded successfully",
                "consultation", savedConsultation
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create consultation: " + e.getMessage()
            ));
        }
    }
}
