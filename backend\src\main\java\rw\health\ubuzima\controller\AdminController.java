package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.repository.HealthRecordRepository;
import rw.health.ubuzima.repository.AppointmentRepository;
import rw.health.ubuzima.repository.HealthFacilityRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AdminController {

    private final UserRepository userRepository;
    private final HealthRecordRepository healthRecordRepository;
    private final AppointmentRepository appointmentRepository;
    private final HealthFacilityRepository healthFacilityRepository;

    // User Management
    @GetMapping("/users")
    public ResponseEntity<Map<String, Object>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) UserRole role,
            @RequestParam(required = false) String search) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<User> users;

            if (search != null && !search.isEmpty()) {
                users = userRepository.searchUsers(search);
            } else if (role != null) {
                users = userRepository.findByRole(role);
            } else {
                users = userRepository.findAll();
            }

            List<UserResponse> userResponses = users.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("users", userResponses);
            response.put("total", users.size());
            response.put("page", page);
            response.put("size", size);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch users: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            UserResponse userResponse = convertToUserResponse(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "user", userResponse
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch user: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/users/{userId}/status")
    public ResponseEntity<Map<String, Object>> updateUserStatus(
            @PathVariable Long userId,
            @RequestBody Map<String, String> request) {
        
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            String statusStr = request.get("status");
            UserStatus status = UserStatus.valueOf(statusStr.toUpperCase());
            
            user.setStatus(status);
            userRepository.save(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "User status updated successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update user status: " + e.getMessage()
            ));
        }
    }

    // Dashboard Statistics
    @GetMapping("/dashboard/stats")
    public ResponseEntity<Map<String, Object>> getDashboardStats() {
        try {
            long totalUsers = userRepository.count();
            long totalClients = userRepository.findByRole(UserRole.CLIENT).size();
            long totalHealthWorkers = userRepository.findByRole(UserRole.HEALTH_WORKER).size();
            long totalAdmins = userRepository.findByRole(UserRole.ADMIN).size();
            long totalHealthRecords = healthRecordRepository.count();
            long totalAppointments = appointmentRepository.count();
            long totalFacilities = healthFacilityRepository.count();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsers", totalUsers);
            stats.put("totalClients", totalClients);
            stats.put("totalHealthWorkers", totalHealthWorkers);
            stats.put("totalAdmins", totalAdmins);
            stats.put("totalHealthRecords", totalHealthRecords);
            stats.put("totalAppointments", totalAppointments);
            stats.put("totalFacilities", totalFacilities);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "stats", stats
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch dashboard stats: " + e.getMessage()
            ));
        }
    }

    // Health Workers Management
    @GetMapping("/health-workers")
    public ResponseEntity<Map<String, Object>> getHealthWorkers() {
        try {
            List<User> healthWorkers = userRepository.findByRole(UserRole.HEALTH_WORKER);
            
            List<UserResponse> healthWorkerResponses = healthWorkers.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "healthWorkers", healthWorkerResponses
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch health workers: " + e.getMessage()
            ));
        }
    }

    // System Health
    @GetMapping("/system/health")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("database", "Connected");
            health.put("activeUsers", userRepository.findByStatus(UserStatus.ACTIVE).size());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "health", health
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "System health check failed: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/users")
    public ResponseEntity<Map<String, Object>> createUser(@RequestBody Map<String, Object> request) {
        try {
            // This would typically use UserService to create a user
            // For now, return a placeholder response
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "User created successfully",
                "user", Map.of("id", "new-user-id")
            ));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create user: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/users/{userId}")
    public ResponseEntity<Map<String, Object>> updateUser(
            @PathVariable Long userId,
            @RequestBody Map<String, Object> request) {
        try {
            User user = userRepository.findById(userId).orElse(null);

            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            // Update user fields
            if (request.get("name") != null) {
                user.setName(request.get("name").toString());
            }
            if (request.get("email") != null) {
                user.setEmail(request.get("email").toString());
            }
            if (request.get("phone") != null) {
                user.setPhone(request.get("phone").toString());
            }

            User updatedUser = userRepository.save(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "User updated successfully",
                "user", convertToUserResponse(updatedUser)
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update user: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/users/{userId}")
    public ResponseEntity<Map<String, Object>> deleteUser(@PathVariable Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);

            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            userRepository.delete(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "User deleted successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to delete user: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/analytics")
    public ResponseEntity<Map<String, Object>> getAnalytics(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            // Generate analytics data
            Map<String, Object> analytics = Map.of(
                "totalUsers", userRepository.count(),
                "activeUsers", userRepository.countByIsActiveTrue(),
                "newUsersThisMonth", userRepository.countNewUsersThisMonth(),
                "usersByRole", userRepository.countUsersByRole()
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "analytics", analytics
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch analytics: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/reports")
    public ResponseEntity<Map<String, Object>> getReports(
            @RequestParam String type,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        try {
            // Generate reports based on type
            List<Map<String, Object>> reports = List.of(
                Map.of("id", "report1", "name", "User Activity Report", "type", type),
                Map.of("id", "report2", "name", "Health Records Report", "type", type)
            );

            return ResponseEntity.ok(Map.of(
                "success", true,
                "reports", reports
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch reports: " + e.getMessage()
            ));
        }
    }

    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(Long.valueOf(user.getId().toString()));
        response.setName(user.getName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setRole(user.getRole());
        response.setFacilityId(user.getFacilityId());
        response.setDistrict(user.getDistrict());
        response.setSector(user.getSector());
        response.setCell(user.getCell());
        response.setVillage(user.getVillage());
        response.setCreatedAt(user.getCreatedAt());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setActive(user.isActive());
        response.setProfileImageUrl(user.getProfilePictureUrl());
        return response;
    }
}
