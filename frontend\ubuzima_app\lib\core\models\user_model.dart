enum UserRole { CLIENT, HEALTH_WORKER, ADMIN, ANONYMOUS }

class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final UserRole role;
  final String? facilityId;
  final String? district;
  final String? sector;
  final String? cell;
  final String? village;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  final String? profileImageUrl;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    this.facilityId,
    this.district,
    this.sector,
    this.cell,
    this.village,
    required this.createdAt,
    this.lastLoginAt,
    this.isActive = true,
    this.profileImageUrl,
  });

  String get roleDisplayName {
    switch (role) {
      case UserRole.CLIENT:
        return 'Umunyangire';
      case UserRole.HEALTH_WORKER:
        return 'Umukozi w\'ubuzima';
      case UserRole.ADMIN:
        return 'Umuyobozi';
      case UserRole.ANONYMOUS:
        return 'Umunyangamugayo';
    }
  }

  String get fullLocation {
    final parts = [
      village,
      cell,
      sector,
      district,
    ].where((part) => part != null && part.isNotEmpty);
    return parts.join(', ');
  }

  bool get canManageUsers => role == UserRole.ADMIN;
  bool get canViewReports =>
      role == UserRole.ADMIN || role == UserRole.HEALTH_WORKER;
  bool get canManageContent =>
      role == UserRole.ADMIN || role == UserRole.HEALTH_WORKER;
  bool get canProvideConsultation =>
      role == UserRole.HEALTH_WORKER || role == UserRole.ADMIN;
  bool get canAccessClientData =>
      role == UserRole.HEALTH_WORKER || role == UserRole.ADMIN;

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    UserRole? role,
    String? facilityId,
    String? district,
    String? sector,
    String? cell,
    String? village,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isActive,
    String? profileImageUrl,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      facilityId: facilityId ?? this.facilityId,
      district: district ?? this.district,
      sector: sector ?? this.sector,
      cell: cell ?? this.cell,
      village: village ?? this.village,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'role': role.name,
      'facilityId': facilityId,
      'district': district,
      'sector': sector,
      'cell': cell,
      'village': village,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'isActive': isActive,
      'profileImageUrl': profileImageUrl,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      role: _parseUserRole(json['role']),
      facilityId: json['facilityId'],
      district: json['district'],
      sector: json['sector'],
      cell: json['cell'],
      village: json['village'],
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      lastLoginAt:
          json['lastLoginAt'] != null
              ? DateTime.parse(json['lastLoginAt'])
              : null,
      isActive: json['active'] ?? json['isActive'] ?? true,
      profileImageUrl: json['profilePictureUrl'] ?? json['profileImageUrl'],
    );
  }

  static UserRole _parseUserRole(dynamic roleValue) {
    if (roleValue == null) return UserRole.CLIENT;

    final roleString = roleValue.toString().toUpperCase();

    // Handle both enum name and string formats
    switch (roleString) {
      case 'CLIENT':
        return UserRole.CLIENT;
      case 'HEALTH_WORKER':
      case 'HEALTHWORKER':
        return UserRole.HEALTH_WORKER;
      case 'ADMIN':
        return UserRole.ADMIN;
      case 'ANONYMOUS':
        return UserRole.ANONYMOUS;
      default:
        return UserRole.CLIENT;
    }
  }
}

// Sample users for demonstration
class SampleUsers {
  static final List<User> users = [
    User(
      id: '1',
      name: 'Mukamana Marie',
      email: '<EMAIL>',
      phone: '+250788123456',
      role: UserRole.CLIENT,
      district: 'Kigali',
      sector: 'Kimisagara',
      cell: 'Nyabugogo',
      village: 'Nyabugogo I',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      lastLoginAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    User(
      id: '2',
      name: 'Dr. Uwimana Jean',
      email: '<EMAIL>',
      phone: '+250788234567',
      role: UserRole.HEALTH_WORKER,
      facilityId: 'HC001',
      district: 'Kigali',
      sector: 'Kimisagara',
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      lastLoginAt: DateTime.now().subtract(const Duration(minutes: 30)),
    ),
    User(
      id: '3',
      name: 'Nkurunziza Paul',
      email: '<EMAIL>',
      phone: '+250788345678',
      role: UserRole.ADMIN,
      facilityId: 'ADMIN001',
      district: 'Kigali',
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      lastLoginAt: DateTime.now().subtract(const Duration(minutes: 15)),
    ),
  ];

  static User getCurrentUser() {
    // In a real app, this would come from authentication service
    return users[0]; // Default to client for demo
  }

  static User? getUserById(String id) {
    try {
      return users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  static List<User> getUsersByRole(UserRole role) {
    return users.where((user) => user.role == role).toList();
  }
}
