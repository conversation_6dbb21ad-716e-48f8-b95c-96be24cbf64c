<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="df44db76-e29c-4171-9069-b9b59e82421d" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/dto/request/LoginRequest.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/exception/AuthenticationException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/exception/DuplicateResourceException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/exception/GlobalExceptionHandler.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/exception/ResourceNotFoundException.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/service/AuthService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/service/UserService.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/rw/health/ubuzima/util/JwtUtil.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="AnnotationType" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zauge3vNVME5QqXww5kjJS9zuw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.UbuzimaApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/WEB/develop/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.editor&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="rw.health.ubuzima.controller" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="UbuzimaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ubuzima-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="rw.health.ubuzima.UbuzimaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.189" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.189" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="df44db76-e29c-4171-9069-b9b59e82421d" name="Changes" comment="" />
      <created>1751981617925</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751981617925</updated>
      <workItem from="1751981619166" duration="1366000" />
      <workItem from="1751983099001" duration="3742000" />
      <workItem from="1751986882256" duration="2947000" />
      <workItem from="1751990311920" duration="6677000" />
      <workItem from="1752045489239" duration="3018000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>