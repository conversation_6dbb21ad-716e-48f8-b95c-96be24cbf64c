{"logs": [{"outputFile": "rw.health.ubuzima.ubuzima_app-mergeDebugResources-72:/values/values.xml", "map": [{"source": "C:\\WEB\\develop\\frontend\\ubuzima_app\\build\\local_auth_android\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,100,144,190,238", "endLines": "2,3,4,5,11", "endColumns": "44,43,45,47,10", "endOffsets": "95,139,185,233,533"}, "to": {"startLines": "110,141,279,283,501", "startColumns": "4,4,4,4,4", "startOffsets": "4358,6641,16042,16311,31196", "endLines": "110,141,279,283,506", "endColumns": "44,43,45,47,10", "endOffsets": "4398,6680,16083,16354,31491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b8025b73987cc94b15b57f0db06b908e\\transformed\\jetified-iris-rtc-4.5.2-build.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "51", "endOffsets": "102"}, "to": {"startLines": "436", "startColumns": "4", "startOffsets": "25398", "endColumns": "51", "endOffsets": "25445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "95,158,299,300,301,302,303,304,305,363,364,365,405,406,463,475,490,491,496,497,498,1574,1758,1761,1767,1773,1776,1782,1786,1789,1796,1802,1805,1811,1816,1821,1828,1830,1836,1842,1850,1855,1862,1867,1873,1877,1884,1888,1894,1900,1903,1907,1908,2834,2849,2988,3026,3168,3419,3437,3501,3511,3521,3528,3534,3638,3807,3824", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3249,7624,17337,17401,17456,17524,17591,17656,17713,21006,21054,21102,23253,23316,28370,29182,30483,30527,30791,30930,30980,99303,113041,113146,113391,113729,113875,114215,114427,114590,114997,115335,115458,115797,116036,116293,116664,116724,117062,117348,117797,118089,118477,118782,119126,119371,119701,119908,120176,120449,120593,120794,120841,163875,164398,171184,172485,177427,184844,185472,187397,187679,187984,188246,188506,192022,198317,198847", "endLines": "95,158,299,300,301,302,303,304,305,363,364,365,405,406,463,475,490,493,496,497,498,1590,1760,1766,1772,1775,1781,1785,1788,1795,1801,1804,1810,1815,1820,1827,1829,1835,1841,1849,1854,1861,1866,1872,1876,1883,1887,1893,1899,1902,1906,1907,1908,2838,2859,3007,3029,3177,3426,3500,3510,3520,3527,3533,3576,3650,3823,3840", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3317,7688,17396,17451,17519,17586,17651,17708,17765,21049,21097,21158,23311,23374,28403,29234,30522,30662,30925,30975,31023,100736,113141,113386,113724,113870,114210,114422,114585,114992,115330,115453,115792,116031,116288,116659,116719,117057,117343,117792,118084,118472,118777,119121,119366,119696,119903,120171,120444,120588,120789,120836,120892,164055,164794,171908,172629,177754,185087,187392,187674,187979,188241,188501,189924,192469,198842,199410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5093ab42d2307deb2d7ac0b7f5718c38\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,354,2295,2301,3682,3690,3705", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,20589,146346,146541,193203,193485,194099", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,354,2300,2305,3689,3704,3720", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,20644,146536,146694,193480,194094,194748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a70ddd560199940b45ffc1a1c4db7f79\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "368,396", "startColumns": "4,4", "startOffsets": "21259,22714", "endColumns": "41,59", "endOffsets": "21296,22769"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\185b052657445071c55336e362c7ed18\\transformed\\jetified-firebase-messaging-23.4.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "479", "startColumns": "4", "startOffsets": "29454", "endColumns": "81", "endOffsets": "29531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0d9d3675465ff69d847e2f781f20c61\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "435", "startColumns": "4", "startOffsets": "25315", "endColumns": "82", "endOffsets": "25393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55520e4df2220e27f13f0bbb7467d11a\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "355,371,399,3084,3089", "startColumns": "4,4,4,4,4", "startOffsets": "20649,21403,22878,175046,175216", "endLines": "355,371,399,3088,3092", "endColumns": "56,64,63,24,24", "endOffsets": "20701,21463,22937,175211,175360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "61,103,104,123,124,156,157,259,260,261,262,263,264,265,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,360,361,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,407,437,438,439,440,441,442,443,495,2020,2021,2025,2026,2030,2174,2175,2843,2860,3030,3063,3093,3126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,3823,3895,5293,5358,7492,7561,14554,14624,14692,14764,14834,14895,14969,16359,16420,16481,16543,16607,16669,16730,16798,16898,16958,17024,17097,17166,17223,17275,18223,18295,18371,18436,18495,18554,18614,18674,18734,18794,18854,18914,18974,19034,19094,19154,19213,19273,19333,19393,19453,19513,19573,19633,19693,19753,19813,19872,19932,19992,20051,20110,20169,20228,20287,20893,20928,21514,21569,21632,21687,21745,21803,21864,21927,21984,22035,22085,22146,22203,22269,22303,22338,23379,25450,25517,25589,25658,25727,25801,25873,30720,129866,129983,130184,130294,130495,141919,141991,164195,164799,172634,174365,175365,176047", "endLines": "61,103,104,123,124,156,157,259,260,261,262,263,264,265,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,360,361,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,407,437,438,439,440,441,442,443,495,2020,2024,2025,2029,2030,2174,2175,2848,2869,3062,3083,3125,3131", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,3890,3978,5353,5419,7556,7619,14619,14687,14759,14829,14890,14964,15037,16415,16476,16538,16602,16664,16725,16793,16893,16953,17019,17092,17161,17218,17270,17332,18290,18366,18431,18490,18549,18609,18669,18729,18789,18849,18909,18969,19029,19089,19149,19208,19268,19328,19388,19448,19508,19568,19628,19688,19748,19808,19867,19927,19987,20046,20105,20164,20223,20282,20341,20923,20958,21564,21627,21682,21740,21798,21859,21922,21979,22030,22080,22141,22198,22264,22298,22333,22368,23444,25512,25584,25653,25722,25796,25868,25956,30786,129978,130179,130289,130490,130619,141986,142053,164393,165095,174360,175041,176042,176209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\294fa292f5b2d15ea9a4a7a32547c712\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2316,2332,2338,3721,3737", "startColumns": "4,4,4,4,4", "startOffsets": "147216,147641,147819,194753,195164", "endLines": "2331,2337,2347,3736,3740", "endColumns": "24,24,24,24,24", "endOffsets": "147636,147814,148098,195159,195286"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "466,467,468,469,470,471,472,473,474", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "28536,28606,28668,28733,28797,28874,28939,29029,29113", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "28601,28663,28728,28792,28869,28934,29024,29108,29177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "117,118,119,120,257,258,464,476,477,478", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4929,4987,5053,5116,14411,14482,28408,29239,29306,29385", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4982,5048,5111,5173,14477,14549,28471,29301,29380,29449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b91be3af319ede480d7185430690ee1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "398", "startColumns": "4", "startOffsets": "22828", "endColumns": "49", "endOffsets": "22873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21f508358b7ef6793f647ddb068091fd\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "36,59,60,91,92,93,94,96,97,98,99,100,101,102,105,106,107,108,111,112,113,114,115,116,121,122,133,134,135,136,137,138,139,140,142,143,144,145,146,147,148,149,150,151,152,153,154,155,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,266,267,272,273,274,275,276,277,278,306,307,308,309,310,311,312,313,349,350,351,352,358,366,367,372,394,400,401,402,403,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,494,499,500,507,508,509,510,518,519,523,527,531,536,542,549,553,557,562,566,570,574,578,582,586,592,596,602,606,612,616,621,625,628,632,638,642,648,652,658,661,665,669,673,677,681,682,683,684,687,690,693,696,700,701,702,703,704,707,709,711,713,718,719,723,729,733,734,736,747,748,752,758,762,763,764,768,795,799,800,804,832,1001,1027,1196,1222,1253,1261,1267,1281,1303,1308,1313,1323,1332,1341,1345,1352,1360,1367,1368,1377,1380,1383,1387,1391,1395,1398,1399,1404,1409,1419,1424,1431,1437,1438,1441,1445,1450,1452,1454,1457,1460,1462,1466,1469,1476,1479,1482,1486,1488,1492,1494,1496,1498,1502,1510,1518,1530,1536,1545,1548,1559,1562,1563,1568,1569,1598,1667,1737,1738,1748,1757,1909,1911,1915,1918,1921,1924,1927,1930,1933,1936,1940,1943,1946,1949,1953,1956,1960,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1986,1988,1989,1990,1991,1992,1993,1994,1995,1997,1998,2000,2001,2003,2005,2006,2008,2009,2010,2011,2012,2013,2015,2016,2017,2018,2019,2031,2033,2035,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2051,2052,2053,2054,2055,2056,2057,2059,2063,2067,2068,2069,2070,2071,2072,2076,2077,2078,2079,2081,2083,2085,2087,2089,2090,2091,2092,2094,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2112,2113,2114,2115,2117,2119,2120,2122,2123,2125,2127,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2142,2143,2144,2145,2147,2148,2149,2150,2151,2153,2155,2157,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2191,2266,2269,2272,2275,2289,2306,2348,2377,2404,2413,2475,2839,2870,3008,3132,3156,3162,3254,3275,3399,3427,3433,3577,3603,3670,3741,3841,3861,3916,3928,3954", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3187,3322,3386,3456,3517,3592,3668,3745,3983,4068,4150,4226,4403,4480,4558,4664,4770,4849,5178,5235,6095,6169,6244,6309,6375,6435,6496,6568,6685,6752,6820,6879,6938,6997,7056,7115,7169,7223,7276,7330,7384,7438,7693,7767,7846,7919,7993,8064,8136,8208,8281,8338,8396,8469,8543,8617,8692,8764,8837,8907,8978,9038,9099,9168,9237,9307,9381,9457,9521,9598,9674,9751,9816,9885,9962,10037,10106,10174,10251,10317,10378,10475,10540,10609,10708,10779,10838,10896,10953,11012,11076,11147,11219,11291,11363,11435,11502,11570,11638,11697,11760,11824,11914,12005,12065,12131,12198,12264,12334,12398,12451,12518,12579,12646,12759,12817,12880,12945,13010,13085,13158,13230,13279,13340,13401,13462,13524,13588,13652,13716,13781,13844,13904,13965,14031,14090,14150,14212,14283,14343,15042,15128,15431,15521,15608,15696,15778,15861,15951,17770,17822,17880,17925,17991,18055,18112,18169,20346,20403,20451,20500,20793,21163,21210,21468,22639,22942,23006,23068,23128,23449,23523,23593,23671,23725,23795,23880,23928,23974,24035,24098,24164,24228,24299,24362,24427,24491,24552,24613,24665,24738,24812,24881,24956,25030,25104,25245,30667,31028,31106,31496,31584,31680,31770,32352,32441,32688,32969,33221,33506,33899,34376,34598,34820,35096,35323,35553,35783,36013,36243,36470,36889,37115,37540,37770,38198,38417,38700,38908,39039,39266,39692,39917,40344,40565,40990,41110,41386,41687,42011,42302,42616,42753,42884,42989,43231,43398,43602,43810,44081,44193,44305,44410,44527,44741,44887,45027,45113,45461,45549,45795,46213,46462,46544,46642,47234,47334,47586,48010,48265,48359,48448,48685,50709,50951,51053,51306,53462,63903,65419,75958,77486,79243,79869,80289,81350,82615,82871,83107,83654,84148,84753,84951,85531,86095,86470,86588,87126,87283,87479,87752,88008,88178,88319,88383,88748,89115,89791,90055,90393,90746,90840,91026,91332,91594,91719,91846,92085,92296,92415,92608,92785,93240,93421,93543,93802,93915,94102,94204,94311,94440,94715,95223,95719,96596,96890,97460,97609,98341,98513,98597,98933,99025,101091,106337,111726,111788,112366,112950,120897,121010,121239,121399,121551,121722,121888,122057,122224,122387,122630,122800,122973,123144,123418,123617,123822,124152,124236,124332,124428,124526,124626,124728,124830,124932,125034,125136,125236,125332,125444,125573,125696,125827,125958,126056,126170,126264,126404,126538,126634,126746,126846,126962,127058,127170,127270,127410,127546,127710,127840,127998,128148,128289,128433,128568,128680,128830,128958,129086,129222,129354,129484,129614,129726,130624,130770,130914,131052,131118,131208,131284,131388,131478,131580,131688,131796,131896,131976,132068,132166,132276,132328,132406,132512,132604,132708,132818,132940,133103,133260,133340,133440,133530,133640,133730,133971,134065,134171,134263,134363,134475,134589,134705,134821,134915,135029,135141,135243,135363,135485,135567,135671,135791,135917,136015,136109,136197,136309,136425,136547,136659,136834,136950,137036,137128,137240,137364,137431,137557,137625,137753,137897,138025,138094,138189,138304,138417,138516,138625,138736,138847,138948,139053,139153,139283,139374,139497,139591,139703,139789,139893,139989,140077,140195,140299,140403,140529,140617,140725,140825,140915,141025,141109,141211,141295,141349,141413,141519,141605,141715,141799,142823,145439,145557,145672,145752,146113,146699,148103,149447,150808,151196,153971,164060,165100,171913,176214,176965,177227,179581,179960,184238,185092,185321,189929,190939,192891,195291,199415,200159,202290,202630,203941", "endLines": "36,59,60,91,92,93,94,96,97,98,99,100,101,102,105,106,107,108,111,112,113,114,115,116,121,122,133,134,135,136,137,138,139,140,142,143,144,145,146,147,148,149,150,151,152,153,154,155,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,266,267,272,273,274,275,276,277,278,306,307,308,309,310,311,312,313,349,350,351,352,358,366,367,372,394,400,401,402,403,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,494,499,500,507,508,509,517,518,522,526,530,535,541,548,552,556,561,565,569,573,577,581,585,591,595,601,605,611,615,620,624,627,631,637,641,647,651,657,660,664,668,672,676,680,681,682,683,686,689,692,695,699,700,701,702,703,706,708,710,712,717,718,722,728,732,733,735,746,747,751,757,761,762,763,767,794,798,799,803,831,1000,1026,1195,1221,1252,1260,1266,1280,1302,1307,1312,1322,1331,1340,1344,1351,1359,1366,1367,1376,1379,1382,1386,1390,1394,1397,1398,1403,1408,1418,1423,1430,1436,1437,1440,1444,1449,1451,1453,1456,1459,1461,1465,1468,1475,1478,1481,1485,1487,1491,1493,1495,1497,1501,1509,1517,1529,1535,1544,1547,1558,1561,1562,1567,1568,1573,1666,1736,1737,1747,1756,1757,1910,1914,1917,1920,1923,1926,1929,1932,1935,1939,1942,1945,1948,1952,1955,1959,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1985,1987,1988,1989,1990,1991,1992,1993,1994,1996,1997,1999,2000,2002,2004,2005,2007,2008,2009,2010,2011,2012,2014,2015,2016,2017,2018,2019,2032,2034,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2050,2051,2052,2053,2054,2055,2056,2058,2062,2066,2067,2068,2069,2070,2071,2075,2076,2077,2078,2080,2082,2084,2086,2088,2089,2090,2091,2093,2095,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2111,2112,2113,2114,2116,2118,2119,2121,2122,2124,2126,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2141,2142,2143,2144,2146,2147,2148,2149,2150,2152,2154,2156,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2265,2268,2271,2274,2288,2294,2315,2376,2403,2412,2474,2833,2842,2897,3025,3155,3161,3167,3274,3398,3418,3432,3436,3582,3637,3681,3806,3860,3915,3927,3953,3960", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3182,3244,3381,3451,3512,3587,3663,3740,3818,4063,4145,4221,4297,4475,4553,4659,4765,4844,4924,5230,5288,6164,6239,6304,6370,6430,6491,6563,6636,6747,6815,6874,6933,6992,7051,7110,7164,7218,7271,7325,7379,7433,7487,7762,7841,7914,7988,8059,8131,8203,8276,8333,8391,8464,8538,8612,8687,8759,8832,8902,8973,9033,9094,9163,9232,9302,9376,9452,9516,9593,9669,9746,9811,9880,9957,10032,10101,10169,10246,10312,10373,10470,10535,10604,10703,10774,10833,10891,10948,11007,11071,11142,11214,11286,11358,11430,11497,11565,11633,11692,11755,11819,11909,12000,12060,12126,12193,12259,12329,12393,12446,12513,12574,12641,12754,12812,12875,12940,13005,13080,13153,13225,13274,13335,13396,13457,13519,13583,13647,13711,13776,13839,13899,13960,14026,14085,14145,14207,14278,14338,14406,15123,15210,15516,15603,15691,15773,15856,15946,16037,17817,17875,17920,17986,18050,18107,18164,18218,20398,20446,20495,20546,20822,21205,21254,21509,22666,23001,23063,23123,23180,23518,23588,23666,23720,23790,23875,23923,23969,24030,24093,24159,24223,24294,24357,24422,24486,24547,24608,24660,24733,24807,24876,24951,25025,25099,25240,25310,30715,31101,31191,31579,31675,31765,32347,32436,32683,32964,33216,33501,33894,34371,34593,34815,35091,35318,35548,35778,36008,36238,36465,36884,37110,37535,37765,38193,38412,38695,38903,39034,39261,39687,39912,40339,40560,40985,41105,41381,41682,42006,42297,42611,42748,42879,42984,43226,43393,43597,43805,44076,44188,44300,44405,44522,44736,44882,45022,45108,45456,45544,45790,46208,46457,46539,46637,47229,47329,47581,48005,48260,48354,48443,48680,50704,50946,51048,51301,53457,63898,65414,75953,77481,79238,79864,80284,81345,82610,82866,83102,83649,84143,84748,84946,85526,86090,86465,86583,87121,87278,87474,87747,88003,88173,88314,88378,88743,89110,89786,90050,90388,90741,90835,91021,91327,91589,91714,91841,92080,92291,92410,92603,92780,93235,93416,93538,93797,93910,94097,94199,94306,94435,94710,95218,95714,96591,96885,97455,97604,98336,98508,98592,98928,99020,99298,106332,111721,111783,112361,112945,113036,121005,121234,121394,121546,121717,121883,122052,122219,122382,122625,122795,122968,123139,123413,123612,123817,124147,124231,124327,124423,124521,124621,124723,124825,124927,125029,125131,125231,125327,125439,125568,125691,125822,125953,126051,126165,126259,126399,126533,126629,126741,126841,126957,127053,127165,127265,127405,127541,127705,127835,127993,128143,128284,128428,128563,128675,128825,128953,129081,129217,129349,129479,129609,129721,129861,130765,130909,131047,131113,131203,131279,131383,131473,131575,131683,131791,131891,131971,132063,132161,132271,132323,132401,132507,132599,132703,132813,132935,133098,133255,133335,133435,133525,133635,133725,133966,134060,134166,134258,134358,134470,134584,134700,134816,134910,135024,135136,135238,135358,135480,135562,135666,135786,135912,136010,136104,136192,136304,136420,136542,136654,136829,136945,137031,137123,137235,137359,137426,137552,137620,137748,137892,138020,138089,138184,138299,138412,138511,138620,138731,138842,138943,139048,139148,139278,139369,139492,139586,139698,139784,139888,139984,140072,140190,140294,140398,140524,140612,140720,140820,140910,141020,141104,141206,141290,141344,141408,141514,141600,141710,141794,141914,145434,145552,145667,145747,146108,146341,147211,149442,150803,151191,153966,163870,164190,166452,172480,176960,177222,177422,179955,184233,184839,185316,185467,190139,192017,193198,198312,200154,202285,202625,203936,204139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8a63b21759398e83092b56262a073cdf\\transformed\\jetified-android-maps-utils-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "353,2179,2183,2186", "startColumns": "4,4,4,4", "startOffsets": "20551,142203,142411,142571", "endLines": "353,2182,2185,2190", "endColumns": "37,12,12,12", "endOffsets": "20584,142406,142566,142818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c092edbccc16347970ed4f22e8da111a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "395", "startColumns": "4", "startOffsets": "22671", "endColumns": "42", "endOffsets": "22709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\223e4c2782dceb65140338abe6972c59\\transformed\\jetified-play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "3191", "startColumns": "4", "startOffsets": "178074", "endLines": "3253", "endColumns": "20", "endOffsets": "179576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\abeed4f47a72eff8f5b4e9bf7f2f3c91\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "404,452", "startColumns": "4,4", "startOffsets": "23185,27001", "endColumns": "67,166", "endOffsets": "23248,27163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9db9b81a577112600e8d2ee7b5450923\\transformed\\jetified-play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "125,126,127,128,129,130,131,132,444,445,446,447,448,449,450,451,453,454,455,456,457,458,459,460,461,3178,3651", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5424,5514,5594,5684,5774,5854,5935,6015,25961,26066,26247,26372,26479,26659,26782,26898,27168,27356,27461,27642,27767,27942,28090,28153,28215,177759,192474", "endLines": "125,126,127,128,129,130,131,132,444,445,446,447,448,449,450,451,453,454,455,456,457,458,459,460,461,3190,3669", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5509,5589,5679,5769,5849,5930,6010,6090,26061,26242,26367,26474,26654,26777,26893,26996,27351,27456,27637,27762,27937,28085,28148,28210,28289,178069,192886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45af1ebc35cbf9d2d2886a132166b73a\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "356,357,362,369,370,389,390,391,392,393", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20706,20746,20963,21301,21356,22373,22427,22479,22528,22589", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20741,20788,21001,21351,21398,22422,22474,22523,22584,22634"}}, {"source": "C:\\WEB\\develop\\frontend\\ubuzima_app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1591,1595", "startColumns": "4,4", "startOffsets": "100741,100922", "endLines": "1594,1597", "endColumns": "12,12", "endOffsets": "100917,101086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75e1bcd7a8b61b1e132d50e7766bfd37\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "268,269,270,280,281,282,359,3583", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15215,15274,15322,16088,16163,16239,20827,190144", "endLines": "268,269,270,280,281,282,359,3602", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15269,15317,15373,16158,16234,16306,20888,190934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b3d51a44ab6b56289d4858158a1ad6dd\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "397", "startColumns": "4", "startOffsets": "22774", "endColumns": "53", "endOffsets": "22823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58a0920e123e93dd6aa702d27ab7530e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2176,2898,2904", "startColumns": "4,4,4,4", "startOffsets": "1213,142058,166457,166668", "endLines": "35,2178,2903,2987", "endColumns": "60,12,24,24", "endOffsets": "1269,142198,166663,171179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bd1be39a4121b41df3cecdb1f1acb24d\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,109,271,462,465,480,481,482,483,484,485,486,487,488,489", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4302,15378,28294,28476,29536,29625,29724,29832,29929,30017,30117,30187,30284,30394", "endLines": "5,7,10,14,33,109,271,462,465,480,481,482,483,484,485,486,487,488,489", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4353,15426,28365,28531,29620,29719,29827,29924,30012,30112,30182,30279,30389,30478"}}]}]}