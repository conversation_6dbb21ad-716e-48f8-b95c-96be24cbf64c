2025-07-09 09:58:11 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 19976 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 09:58:11 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 09:58:11 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 09:58:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 09:58:13 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 148 ms. Found 12 JPA repository interfaces.
2025-07-09 09:58:14 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-09 09:58:14 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:58:14 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 09:58:14 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:58:14 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3344 ms
2025-07-09 09:58:15 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 09:58:15 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 09:58:15 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 09:58:16 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 09:58:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 09:58:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5de335cf
2025-07-09 09:58:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 09:58:16 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 09:58:18 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 09:58:19 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 09:58:19 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 09:58:21 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 09:58:22 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 973caa78-a5cc-4ff7-a13d-40ad17aecb80

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 09:58:22 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 09:58:22 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@33145371, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1829aa45, org.springframework.security.web.context.SecurityContextHolderFilter@6e85b19d, org.springframework.security.web.header.HeaderWriterFilter@ebf1fd1, org.springframework.web.filter.CorsFilter@6212232b, org.springframework.security.web.authentication.logout.LogoutFilter@7d7eabb2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c2475de, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5a0be3d0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6c057152, org.springframework.security.web.session.SessionManagementFilter@247091d5, org.springframework.security.web.access.ExceptionTranslationFilter@57fbf72e, org.springframework.security.web.access.intercept.AuthorizationFilter@1bd66900]
2025-07-09 09:58:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 09:58:23 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 13.464 seconds (process running for 16.357)
2025-07-09 09:58:24 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 09:58:24 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 09:58:24 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 09:58:24 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 09:58:24 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 09:58:24 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 09:58:24 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 09:58:24 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-09 10:44:26 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 10:44:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 10:44:26 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 11:52:17 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 5812 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 11:52:17 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 11:52:17 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 11:52:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 11:52:20 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 142 ms. Found 13 JPA repository interfaces.
2025-07-09 11:52:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 11:52:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 11:52:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 11:52:22 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 11:52:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4878 ms
2025-07-09 11:52:22 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 11:52:23 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 11:52:23 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 11:52:23 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 11:52:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 11:52:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3063be68
2025-07-09 11:52:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 11:52:24 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 11:52:26 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 11:52:26 [main] DEBUG org.hibernate.SQL - 
    create table file_uploads (
        id varchar(255) not null,
        file_path varchar(255) not null,
        file_type varchar(255) not null,
        filename varchar(255) not null,
        mime_type varchar(255),
        original_filename varchar(255) not null,
        size bigint not null,
        uploaded_at timestamp(6) not null,
        user_id varchar(255),
        primary key (id)
    )
2025-07-09 11:52:27 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 11:52:27 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 11:52:30 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 11:52:30 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a8f0e10f-fc0e-4bf0-a3ab-548172793166

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 11:52:31 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 11:52:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4da20418, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7fc386b5, org.springframework.security.web.context.SecurityContextHolderFilter@6ada92a2, org.springframework.security.web.header.HeaderWriterFilter@35d58b38, org.springframework.web.filter.CorsFilter@2d814b25, org.springframework.security.web.authentication.logout.LogoutFilter@e9f436e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64904416, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d95b464, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7a2d6483, org.springframework.security.web.session.SessionManagementFilter@687a3bc8, org.springframework.security.web.access.ExceptionTranslationFilter@2d471a7d, org.springframework.security.web.access.intercept.AuthorizationFilter@1669899]
2025-07-09 11:52:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 11:52:32 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 15.802 seconds (process running for 19.331)
2025-07-09 11:52:32 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 11:52:32 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 11:52:32 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 11:52:32 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 11:52:32 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 14:00:45 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 8856 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 14:00:45 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 14:00:45 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 14:00:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 14:00:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 154 ms. Found 13 JPA repository interfaces.
2025-07-09 14:00:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 14:00:49 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 14:00:49 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 14:00:49 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 14:00:49 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4059 ms
2025-07-09 14:00:49 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 14:00:50 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 14:00:50 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 14:00:50 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 14:00:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 14:00:51 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@124ff64d
2025-07-09 14:00:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 14:00:51 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 14:00:54 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 14:00:55 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 14:00:56 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 14:00:59 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 14:00:59 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: b4208dbb-d866-4602-b504-df33b4d1dceb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 14:01:00 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 14:01:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@133c43c2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3486de4d, org.springframework.security.web.context.SecurityContextHolderFilter@e7d7618, org.springframework.security.web.header.HeaderWriterFilter@3d6d0d9, org.springframework.web.filter.CorsFilter@2833808, org.springframework.security.web.authentication.logout.LogoutFilter@3bf507ae, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@18969032, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@23afde4a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@687a3bc8, org.springframework.security.web.session.SessionManagementFilter@184ed04d, org.springframework.security.web.access.ExceptionTranslationFilter@788a2594, org.springframework.security.web.access.intercept.AuthorizationFilter@59139fb7]
2025-07-09 14:01:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 14:01:01 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 16.479 seconds (process running for 24.647)
2025-07-09 14:01:01 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 14:01:01 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 14:01:01 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 14:01:01 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 14:01:01 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 14:01:01 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 14:01:01 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 14:01:01 [RMI TCP Connection(2)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-09 14:26:15 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 14:26:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 14:26:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 14:26:20 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 22720 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 14:26:20 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 14:26:20 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 14:26:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 14:26:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 71 ms. Found 13 JPA repository interfaces.
2025-07-09 14:26:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 14:26:23 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 14:26:23 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 14:26:23 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 14:26:23 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2955 ms
2025-07-09 14:26:23 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 14:26:23 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 14:26:23 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 14:26:23 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 14:26:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 14:26:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6068cda1
2025-07-09 14:26:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 14:26:24 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 14:26:25 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 14:26:25 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 14:26:25 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 14:26:26 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 14:26:26 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 8e6ece4f-50df-4152-89f5-2fae31eff558

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 14:26:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 14:26:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@60f9b45e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@540d6a81, org.springframework.security.web.context.SecurityContextHolderFilter@4e3617fe, org.springframework.security.web.header.HeaderWriterFilter@68b30f2d, org.springframework.web.filter.CorsFilter@2960f781, org.springframework.security.web.authentication.logout.LogoutFilter@5f4db912, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@730e92ff, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7cead0f2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@109009dc, org.springframework.security.web.session.SessionManagementFilter@ed80697, org.springframework.security.web.access.ExceptionTranslationFilter@67e43f51, org.springframework.security.web.access.intercept.AuthorizationFilter@861106e]
2025-07-09 14:26:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 14:26:28 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 8.451 seconds (process running for 9.581)
2025-07-09 14:26:28 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 14:26:28 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 14:26:28 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 14:26:28 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 14:26:28 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 14:26:28 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 14:26:28 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 14:26:28 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 15:01:20 [RMI TCP Accept-0] WARN  sun.rmi.transport.tcp - RMI TCP Accept-0: accept loop for ServerSocket[addr=0.0.0.0/0.0.0.0,localport=56576] throws
java.io.IOException: The server sockets created using the LocalRMIServerSocketFactory only accept connections from clients running on the host where the RMI remote objects have been exported.
	at jdk.management.agent/sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(LocalRMIServerSocketFactory.java:114)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(TCPTransport.java:413)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(TCPTransport.java:377)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-09 15:12:08 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 15:12:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 15:12:08 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 15:12:13 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 24780 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 15:12:13 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 15:12:13 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 15:12:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 15:12:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 88 ms. Found 13 JPA repository interfaces.
2025-07-09 15:12:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 15:12:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 15:12:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 15:12:17 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 15:12:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3524 ms
2025-07-09 15:12:17 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 15:12:17 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 15:12:17 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 15:12:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 15:12:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 15:12:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@644e6a8e
2025-07-09 15:12:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 15:12:18 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 15:12:19 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 15:12:19 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 15:12:19 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 15:12:21 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 15:12:21 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: eeb921b5-ab11-4328-a903-4888f264982a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 15:12:21 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 15:12:21 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@289254fe, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c6ab0a6, org.springframework.security.web.context.SecurityContextHolderFilter@18593a44, org.springframework.security.web.header.HeaderWriterFilter@4d71da5a, org.springframework.web.filter.CorsFilter@24ceafbe, org.springframework.security.web.authentication.logout.LogoutFilter@228a6483, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6ace6b0a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@bebbcbd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7493a0e9, org.springframework.security.web.session.SessionManagementFilter@5f4db912, org.springframework.security.web.access.ExceptionTranslationFilter@692a65b1, org.springframework.security.web.access.intercept.AuthorizationFilter@41acf46d]
2025-07-09 15:12:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 15:12:22 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 9.309 seconds (process running for 10.382)
2025-07-09 15:12:22 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 15:12:22 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 15:12:22 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 15:12:22 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 15:12:22 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 15:12:22 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 15:12:22 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 15:12:22 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 15:23:42 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 15:23:42 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 15:23:42 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 15:23:47 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 22844 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 15:23:47 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 15:23:47 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 15:23:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 15:23:48 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 65 ms. Found 13 JPA repository interfaces.
2025-07-09 15:23:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 15:23:49 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 15:23:49 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 15:23:49 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 15:23:49 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1956 ms
2025-07-09 15:23:49 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 15:23:49 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 15:23:49 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 15:23:49 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 15:23:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 15:23:50 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@24f3fb87
2025-07-09 15:23:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 15:23:50 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 15:23:51 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 15:23:51 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 15:23:51 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 15:23:52 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 15:23:53 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 92cf3824-f76f-4f4c-9a2b-d2da0d449251

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 15:23:53 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 15:23:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@79befb56, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e4f2a5c, org.springframework.security.web.context.SecurityContextHolderFilter@78227f53, org.springframework.security.web.header.HeaderWriterFilter@3d607d35, org.springframework.web.filter.CorsFilter@4fcbe248, org.springframework.security.web.authentication.logout.LogoutFilter@3ce54d03, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5ec75175, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@550cfdc6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@228a6483, org.springframework.security.web.session.SessionManagementFilter@470bd375, org.springframework.security.web.access.ExceptionTranslationFilter@2c8cced4, org.springframework.security.web.access.intercept.AuthorizationFilter@530cce6f]
2025-07-09 15:23:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 15:23:53 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 7.151 seconds (process running for 8.074)
2025-07-09 15:23:54 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 15:23:54 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 15:23:54 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 15:23:54 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 15:23:54 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 15:23:54 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 15:23:54 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 15:23:54 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 15:43:25 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-09 15:43:25 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 15:43:25 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-09 15:43:58 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-09 15:43:58 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 15:43:58 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-09 16:05:53 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /auth/register
2025-07-09 16:05:53 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 16:05:53 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /auth/register
2025-07-09 16:05:54 [http-nio-8080-exec-1] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:267)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:127)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-07-09 16:07:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/register
2025-07-09 16:07:42 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 16:07:42 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/register
2025-07-09 16:07:43 [http-nio-8080-exec-3] ERROR r.h.u.e.GlobalExceptionHandler - Unexpected error: 
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `rw.health.ubuzima.enums.UserRole` from String "CLIENT": not one of the values accepted for Enum class: [healthWorker, client, admin]
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:406)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:354)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:184)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:161)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:135)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `rw.health.ubuzima.enums.UserRole` from String "CLIENT": not one of the values accepted for Enum class: [healthWorker, client, admin]
 at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 104] (through reference chain: rw.health.ubuzima.dto.request.UserCreateRequest["role"])
	at com.fasterxml.jackson.databind.exc.InvalidFormatException.from(InvalidFormatException.java:67)
	at com.fasterxml.jackson.databind.DeserializationContext.weirdStringException(DeserializationContext.java:2002)
	at com.fasterxml.jackson.databind.DeserializationContext.handleWeirdStringValue(DeserializationContext.java:1230)
	at com.fasterxml.jackson.databind.deser.std.EnumDeserializer._deserializeAltString(EnumDeserializer.java:415)
	at com.fasterxml.jackson.databind.deser.std.EnumDeserializer._fromString(EnumDeserializer.java:279)
	at com.fasterxml.jackson.databind.deser.std.EnumDeserializer.deserialize(EnumDeserializer.java:248)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserializeFromObject(BeanDeserializer.java:392)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:185)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2105)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1481)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:395)
	... 130 common frames omitted
2025-07-09 16:12:50 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/register
2025-07-09 16:12:50 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 16:12:50 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/register
2025-07-09 16:12:51 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 16:12:51 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.phone=? 
    fetch
        first ? rows only
2025-07-09 16:12:52 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.User */insert 
    into
        users (cell, created_at, date_of_birth, district, email, email_verified, emergency_contact, facility_id, gender, last_login_at, name, password_hash, phone, phone_verified, preferred_language, profile_picture_url, role, sector, status, updated_at, version, village) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-09 18:44:21 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 18:44:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 18:44:21 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 18:44:36 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 27820 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 18:44:36 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 18:44:36 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 18:44:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 18:44:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 144 ms. Found 13 JPA repository interfaces.
2025-07-09 18:44:42 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 18:44:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 18:44:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 18:44:42 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 18:44:42 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5828 ms
2025-07-09 18:44:43 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 18:44:43 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 18:44:43 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 18:44:44 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 18:44:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 18:44:44 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5e2b512b
2025-07-09 18:44:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 18:44:44 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 18:44:47 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 18:44:47 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 18:44:48 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 18:44:51 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 18:44:51 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 329811cc-2dff-4b3e-8de7-16bbd28e6b63

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 18:44:52 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 18:44:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@79c67d42, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b7afcac, org.springframework.security.web.context.SecurityContextHolderFilter@2e59b658, org.springframework.security.web.header.HeaderWriterFilter@444d9de1, org.springframework.web.filter.CorsFilter@401f8bad, org.springframework.security.web.authentication.logout.LogoutFilter@732172ad, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6dd08a62, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@275c1aed, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@42875911, org.springframework.security.web.session.SessionManagementFilter@2c8cced4, org.springframework.security.web.access.ExceptionTranslationFilter@721df027, org.springframework.security.web.access.intercept.AuthorizationFilter@3ce54d03]
2025-07-09 18:44:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 18:44:54 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 18.762 seconds (process running for 20.304)
2025-07-09 18:44:54 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 18:44:54 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 18:44:54 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 18:44:54 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 18:44:54 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 18:44:55 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 18:44:55 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 18:44:55 [RMI TCP Connection(4)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-07-09 20:06:34 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-09 20:06:34 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:06:35 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-09 20:06:35 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hr1_0.id,
        hr1_0.created_at,
        hr1_0.is_verified,
        hr1_0.notes,
        hr1_0.record_type,
        hr1_0.recorded_at,
        hr1_0.recorded_by,
        hr1_0.unit,
        hr1_0.updated_at,
        hr1_0.user_id,
        hr1_0.value,
        hr1_0.verification_notes,
        hr1_0.version 
    from
        health_records hr1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-09 20:06:36 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-09 20:06:36 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:06:36 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-09 20:06:36 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-09 20:09:23 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 20:09:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-09 20:09:23 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-09 20:09:29 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 18044 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-09 20:09:29 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-09 20:09:29 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-09 20:09:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-09 20:09:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 229 ms. Found 13 JPA repository interfaces.
2025-07-09 20:09:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-09 20:09:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 20:09:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-09 20:09:35 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-09 20:09:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6176 ms
2025-07-09 20:09:36 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-09 20:09:36 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-09 20:09:36 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-09 20:09:37 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-09 20:09:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-09 20:09:37 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@ba87c11
2025-07-09 20:09:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-09 20:09:38 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-09 20:09:41 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-09 20:09:41 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-09 20:09:42 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-09 20:09:46 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-09 20:09:46 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ab698b58-2a10-4160-b8b9-f962ce44dbbf

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-09 20:09:47 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-09 20:09:47 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@e25f2fc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@530cce6f, org.springframework.security.web.context.SecurityContextHolderFilter@470bd375, org.springframework.security.web.header.HeaderWriterFilter@28c8c41a, org.springframework.web.filter.CorsFilter@6891f0e5, org.springframework.security.web.authentication.logout.LogoutFilter@2cd8310d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@72d5daf9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@692a65b1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d678643, org.springframework.security.web.session.SessionManagementFilter@5c803ec4, org.springframework.security.web.access.ExceptionTranslationFilter@7c26b384, org.springframework.security.web.access.intercept.AuthorizationFilter@2c721bb5]
2025-07-09 20:09:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-09 20:09:49 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 20.935 seconds (process running for 22.741)
2025-07-09 20:09:49 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 20:09:49 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 20:09:49 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 20:09:49 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-09 20:09:49 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-09 20:09:50 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 20:09:50 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 20:09:50 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-09 20:11:01 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/register
2025-07-09 20:11:01 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:11:01 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/register
2025-07-09 20:11:02 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-09 20:11:02 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.phone=? 
    fetch
        first ? rows only
2025-07-09 20:11:02 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.User */insert 
    into
        users (cell, created_at, date_of_birth, district, email, email_verified, emergency_contact, facility_id, gender, last_login_at, name, password_hash, phone, phone_verified, preferred_language, profile_picture_url, role, sector, status, updated_at, version, village) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-09 20:13:00 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-09 20:13:00 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:13:00 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-09 20:13:00 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:13:01 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:13:01 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:13:01 [http-nio-8080-exec-3] DEBUG org.hibernate.SQL - 
    /* update
        for rw.health.ubuzima.entity.User */update users 
    set
        cell=?,
        date_of_birth=?,
        district=?,
        email=?,
        email_verified=?,
        emergency_contact=?,
        facility_id=?,
        gender=?,
        last_login_at=?,
        name=?,
        password_hash=?,
        phone=?,
        phone_verified=?,
        preferred_language=?,
        profile_picture_url=?,
        role=?,
        sector=?,
        status=?,
        updated_at=?,
        version=?,
        village=? 
    where
        id=? 
        and version=?
2025-07-09 20:22:50 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-09 20:22:50 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:22:50 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-09 20:22:51 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:22:51 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:22:51 [http-nio-8080-exec-4] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:26:38 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health
2025-07-09 20:26:38 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:26:38 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health
2025-07-09 20:26:39 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /health-records
2025-07-09 20:26:39 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:26:39 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /health-records
2025-07-09 20:26:39 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        hr1_0.id,
        hr1_0.created_at,
        hr1_0.is_verified,
        hr1_0.notes,
        hr1_0.record_type,
        hr1_0.recorded_at,
        hr1_0.recorded_by,
        hr1_0.unit,
        hr1_0.updated_at,
        hr1_0.user_id,
        hr1_0.value,
        hr1_0.verification_notes,
        hr1_0.version 
    from
        health_records hr1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-09 20:26:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /appointments
2025-07-09 20:26:40 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:26:40 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /appointments
2025-07-09 20:26:40 [http-nio-8080-exec-10] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        a1_0.id,
        a1_0.appointment_type,
        a1_0.cancellation_reason,
        a1_0.cancelled_at,
        a1_0.completed_at,
        a1_0.created_at,
        a1_0.duration_minutes,
        a1_0.health_facility_id,
        a1_0.health_worker_id,
        a1_0.notes,
        a1_0.reason,
        a1_0.reminder_sent,
        a1_0.scheduled_date,
        a1_0.status,
        a1_0.updated_at,
        a1_0.user_id,
        a1_0.version 
    from
        appointments a1_0 
    offset
        ? rows 
    fetch
        first ? rows only
2025-07-09 20:58:39 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/login
2025-07-09 20:58:39 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-09 20:58:39 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/login
2025-07-09 20:58:39 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:58:39 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
2025-07-09 20:58:39 [http-nio-8080-exec-1] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id,
        u1_0.cell,
        u1_0.created_at,
        u1_0.date_of_birth,
        u1_0.district,
        u1_0.email,
        u1_0.email_verified,
        u1_0.emergency_contact,
        u1_0.facility_id,
        u1_0.gender,
        u1_0.last_login_at,
        u1_0.name,
        u1_0.password_hash,
        u1_0.phone,
        u1_0.phone_verified,
        u1_0.preferred_language,
        u1_0.profile_picture_url,
        u1_0.role,
        u1_0.sector,
        u1_0.status,
        u1_0.updated_at,
        u1_0.version,
        u1_0.village 
    from
        users u1_0 
    where
        u1_0.email=?
