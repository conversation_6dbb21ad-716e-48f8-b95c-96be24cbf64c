package rw.health.ubuzima.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
public class JwtUtil {

    @Value("${ubuzima.jwt.secret}")
    private String secret;

    @Value("${ubuzima.jwt.expiration}")
    private Long expiration;

    @Value("${ubuzima.jwt.refresh-expiration}")
    private Long refreshExpiration;

    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        try {
            final Claims claims = extractAllClaims(token);
            return claimsResolver.apply(claims);
        } catch (Exception e) {
            throw new RuntimeException("Error extracting claim from token", e);
        }
    }

    private Claims extractAllClaims(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (Exception e) {
            throw new RuntimeException("Error parsing JWT token", e);
        }
    }

    private Boolean isTokenExpired(String token) {
        try {
            Date expiration = extractExpiration(token);
            return expiration != null && expiration.before(new Date());
        } catch (Exception e) {
            return true; // Consider expired if we can't determine expiration
        }
    }

    public String generateToken(String username, String role, Long userId) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }
        if (role == null || role.trim().isEmpty()) {
            throw new IllegalArgumentException("Role cannot be null or empty");
        }
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }

        Map<String, Object> claims = new HashMap<>();
        claims.put("role", role);
        claims.put("userId", userId);
        return createToken(claims, username, expiration);
    }

    public String generateRefreshToken(String username, String role, Long userId) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }
        if (role == null || role.trim().isEmpty()) {
            throw new IllegalArgumentException("Role cannot be null or empty");
        }
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }

        Map<String, Object> claims = new HashMap<>();
        claims.put("role", role);
        claims.put("userId", userId);
        claims.put("type", "refresh");
        return createToken(claims, username, refreshExpiration);
    }

    private String createToken(Map<String, Object> claims, String subject, Long expiration) {
        try {
            if (claims == null) {
                claims = new HashMap<>();
            }
            if (subject == null || subject.trim().isEmpty()) {
                throw new IllegalArgumentException("Subject cannot be null or empty");
            }
            if (expiration == null || expiration <= 0) {
                throw new IllegalArgumentException("Expiration must be positive");
            }

            Date now = new Date(System.currentTimeMillis());
            Date expiryDate = new Date(System.currentTimeMillis() + expiration);

            return Jwts.builder()
                    .claims(claims)
                    .subject(subject)
                    .issuedAt(now)
                    .expiration(expiryDate)
                    .signWith(getSigningKey())
                    .compact();
        } catch (Exception e) {
            throw new RuntimeException("Error creating JWT token", e);
        }
    }

    public Boolean validateToken(String token, String username) {
        try {
            if (token == null || username == null) {
                return false;
            }
            final String extractedUsername = extractUsername(token);
            return (extractedUsername != null && extractedUsername.equals(username) && !isTokenExpired(token));
        } catch (Exception e) {
            return false;
        }
    }

    public String extractRole(String token) {
        try {
            return extractClaim(token, claims -> {
                Object role = claims.get("role");
                return role != null ? role.toString() : null;
            });
        } catch (Exception e) {
            return null;
        }
    }

    public Long extractUserId(String token) {
        try {
            return extractClaim(token, claims -> {
                Object userId = claims.get("userId");
                if (userId instanceof Number) {
                    return ((Number) userId).longValue();
                } else if (userId instanceof String) {
                    return Long.parseLong((String) userId);
                }
                return null;
            });
        } catch (Exception e) {
            return null;
        }
    }

    public boolean isRefreshToken(String token) {
        try {
            String type = extractClaim(token, claims -> claims.get("type", String.class));
            return "refresh".equals(type);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isTokenValid(String token) {
        try {
            return token != null && !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }

    public String refreshAccessToken(String refreshToken) {
        try {
            if (!isRefreshToken(refreshToken) || !isTokenValid(refreshToken)) {
                throw new RuntimeException("Invalid refresh token");
            }

            String username = extractUsername(refreshToken);
            String role = extractRole(refreshToken);
            Long userId = extractUserId(refreshToken);

            return generateToken(username, role, userId);
        } catch (Exception e) {
            throw new RuntimeException("Error refreshing access token", e);
        }
    }

    public Map<String, Object> getTokenClaims(String token) {
        try {
            Claims claims = extractAllClaims(token);
            Map<String, Object> tokenClaims = new HashMap<>();
            tokenClaims.put("username", claims.getSubject());
            tokenClaims.put("role", claims.get("role"));
            tokenClaims.put("userId", claims.get("userId"));
            tokenClaims.put("issuedAt", claims.getIssuedAt());
            tokenClaims.put("expiration", claims.getExpiration());
            tokenClaims.put("type", claims.get("type"));
            return tokenClaims;
        } catch (Exception e) {
            throw new RuntimeException("Error extracting token claims", e);
        }
    }

    public boolean isTokenFormatValid(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // Basic JWT format check (header.payload.signature)
        String[] parts = token.split("\\.");
        return parts.length == 3;
    }

    public String getTokenType(String token) {
        try {
            String type = extractClaim(token, claims -> {
                Object typeObj = claims.get("type");
                return typeObj != null ? typeObj.toString() : "access";
            });
            return type != null ? type : "access";
        } catch (Exception e) {
            return "unknown";
        }
    }

    public boolean isAccessToken(String token) {
        String type = getTokenType(token);
        return "access".equals(type) || type == null;
    }

    public Date getTokenIssuedAt(String token) {
        try {
            return extractClaim(token, Claims::getIssuedAt);
        } catch (Exception e) {
            return null;
        }
    }

    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = extractExpiration(token);
            if (expiration == null) {
                return 0;
            }
            long remaining = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0, remaining);
        } catch (Exception e) {
            return 0;
        }
    }

    public boolean validateRefreshToken(String token) {
        try {
            return !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }

    public String generatePasswordResetToken(String email) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("type", "password_reset");
        return createToken(claims, email, 3600000L); // 1 hour expiration
    }

    public boolean validatePasswordResetToken(String token) {
        try {
            Claims claims = extractAllClaims(token);
            String type = claims.get("type", String.class);
            return "password_reset".equals(type) && !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }
}
